class Validators {
  static String? validateMobile(String? value) {
    if (value == null || value.isEmpty) {
      return 'Mobile number cannot be empty.';
    }
    // This is a very basic validation. For production, use a more robust regex
    // or a library that handles international phone numbers.
    if (value.length < 9) {
      return 'Enter a valid mobile number.';
    }
    return null;
  }

  static String? validateLiters(String? value) {
    if (value == null || value.isEmpty) {
      return 'Liters cannot be empty.';
    }
    final number = double.tryParse(value);
    if (number == null || number <= 0) {
      return 'Please enter a valid number greater than 0.';
    }
    return null;
  }
}
