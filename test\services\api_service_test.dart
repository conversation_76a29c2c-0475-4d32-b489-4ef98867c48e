import 'dart:convert';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'api_service_test.mocks.dart';

@GenerateMocks([http.Client, LoggingService])
void main() {
  late MockClient mockClient;
  late MockLoggingService mockLogger;
  late ApiService apiService;

  setUp(() {
    mockClient = MockClient();
    mockLogger = MockLoggingService();
    apiService = ApiService(client: mockClient, logger: mockLogger);
  });

  group('ApiService post', () {
    test('returns decoded JSON when the response code is 200', () async {
      final responsePayload = {'status': 'success', 'data': 'some data'};
      when(mockClient.post(any,
              headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer(
              (_) async => http.Response(json.encode(responsePayload), 200));

      final result = await apiService.post('/test', body: {});

      expect(result, isA<Map<String, dynamic>>());
      expect(result['status'], 'success');
    });

    test('throws ApiException when the response code is not 200', () async {
      final errorPayload = {'message': 'Invalid credentials'};
      when(mockClient.post(any,
              headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer(
              (_) async => http.Response(json.encode(errorPayload), 401));

      final call = apiService.post('/test', body: {});

      expect(call, throwsA(isA<ApiException>()));
      // You can also check the message and status code
      expect(
          call,
          throwsA(predicate((e) =>
              e is ApiException &&
              e.message == 'Invalid credentials' &&
              e.statusCode == 401)));
    });
  });
}
