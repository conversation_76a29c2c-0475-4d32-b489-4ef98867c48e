import 'package:waie_digital_coupon_app/src/config/router/app_router.dart';
import 'package:waie_digital_coupon_app/src/core/models/driver_dc.dart';
import 'package:waie_digital_coupon_app/src/core/providers/coupon_provider.dart';
import 'package:waie_digital_coupon_app/src/core/utils/validators.dart';
import 'package:waie_digital_coupon_app/src/ui/widgets/common/error_display.dart';
import 'package:waie_digital_coupon_app/src/ui/widgets/common/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class CreateCouponScreen extends StatefulWidget {
  final bool isFromMenu;
  const CreateCouponScreen({super.key, required this.isFromMenu});

  @override
  State<CreateCouponScreen> createState() => _CreateCouponScreenState();
}

class _CreateCouponScreenState extends State<CreateCouponScreen> {
  final _formKey = GlobalKey<FormState>();
  final _litersController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final couponProvider =
          Provider.of<CouponProvider>(context, listen: false);
      couponProvider.addListener(_onCouponStateChanged);
      _litersController.addListener(() {
        couponProvider.setFillingLiters(_litersController.text);
      });
    });
  }

  void _onCouponStateChanged() {
    final couponProvider = Provider.of<CouponProvider>(context, listen: false);
    if (couponProvider.state == CouponState.error) {
      ErrorDisplay.show(context, message: couponProvider.errorMessage);
    } else if (couponProvider.state == CouponState.qrGenerated) {
      context.push(AppRouter.displayCoupon, extra: widget.isFromMenu);
    }
  }

  @override
  void dispose() {
    Provider.of<CouponProvider>(context, listen: false)
        .removeListener(_onCouponStateChanged);
    _litersController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final couponProvider = context.watch<CouponProvider>();
    final driverDetails = couponProvider.driverDetails;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.createDigitalCoupon),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (widget.isFromMenu) {
              // Logic to go back to a menu screen if it exists
              context.pop();
            } else {
              // Go back to the login/verification screen, resetting state
              Provider.of<CouponProvider>(context, listen: false).reset();
              Provider.of<AuthProvider>(context, listen: false).reset();
              context.go(AppRouter.verifyMobile);
            }
          },
        ),
      ),
      body: _buildBody(context, l10n, couponProvider, driverDetails),
    );
  }

  Widget _buildBody(BuildContext context, AppLocalizations l10n,
      CouponProvider couponProvider, DriverDc driverDetails) {
    if (couponProvider.state == CouponState.loading &&
        driverDetails.platenos.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (driverDetails.platenos.isEmpty) {
      return Center(
        child: Text(
          "No vehicle details found.",
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      );
    }

    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildDetailRow(l10n.plateNo, driverDetails.plateno),
          _buildDetailRow(l10n.userID, driverDetails.custid),
          _buildDetailRow(l10n.fuelType, driverDetails.fueltype),
          _buildDetailRow(l10n.quotaValue, driverDetails.quotavalue),
          _buildDetailRow(l10n.quotaRemains, driverDetails.remquotavalue),
          const Divider(height: 32),

          // Plate Number Dropdown
          DropdownButtonFormField<String>(
            value: couponProvider.selectedPlateNo,
            decoration: InputDecoration(
              labelText: l10n.plateNumber,
              border: const OutlineInputBorder(),
            ),
            items: driverDetails.platenos
                .map((plate) => DropdownMenuItem(
                      value: plate.plateNo,
                      child: Text(plate.plateNo),
                    ))
                .toList(),
            onChanged: (value) {
              if (value != null) {
                couponProvider.selectPlateNumber(value);
              }
            },
          ),
          const SizedBox(height: 16),

          // Filling Liters Input
          TextFormField(
            controller: _litersController,
            decoration: InputDecoration(
              labelText: l10n.fillingLiters,
              hintText: '0',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))
            ],
            validator: Validators.validateLiters,
          ),
          const SizedBox(height: 32),

          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                couponProvider.generateQrCode();
              }
            },
            child: Text(l10n.generateQRCode),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: Theme.of(context).textTheme.titleMedium),
          Text(value,
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
