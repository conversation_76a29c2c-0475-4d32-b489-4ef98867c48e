import 'package:waie_digital_coupon_app/src/core/providers/coupon_provider.dart';
import 'package:waie_digital_coupon_app/src/ui/widgets/dialogs/confirm_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

class DisplayCouponScreen extends StatelessWidget {
  final bool isFromMenu;
  const DisplayCouponScreen({super.key, required this.isFromMenu});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final couponProvider = context.watch<CouponProvider>();
    final driverDetails = couponProvider.driverDetails;
    final qrData = couponProvider.qrCodeData;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.digitalCoupon),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: TextButton(
              onPressed: () => context.pop(),
              child: Text(l10n.newCoupon),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildDetailRow(l10n.userID, driverDetails.custid, context),
                  const SizedBox(height: 24),
                  if (qrData.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        // Simulate attendant scanning
                        showDialog(
                          context: context,
                          builder: (_) => ConfirmDialog(
                            isFromMenu: isFromMenu,
                            liters: couponProvider.fillingLiters,
                          ),
                        );
                      },
                      child: QrImageView(
                        data: qrData,
                        version: QrVersions.auto,
                        size: 200.0,
                        backgroundColor: Colors.white,
                      ),
                    ),
                  const SizedBox(height: 24),
                  Text(
                    "717022", // This seems to be a static code in the original app
                    style: Theme.of(context).textTheme.headlineSmall,
                  )
                ],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              l10n.digitalCouponNote,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(l10n.note1, style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 4),
            Text(l10n.note2, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String title, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        Text(value,
            style: Theme.of(context)
                .textTheme
                .bodyLarge
                ?.copyWith(fontWeight: FontWeight.bold)),
      ],
    );
  }
}
