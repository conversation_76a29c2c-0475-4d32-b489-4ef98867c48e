import 'dart:convert';

DriverDc driverDcFromJson(String str) => DriverDc.fromJson(jsonDecode(str));

class DriverDc {
  final String custid;
  final String mobileno;
  final String otp;
  final String serialid;
  final String plateno;
  final String fueltype;
  final String quotavalue;
  final String remquotavalue;
  final String stnNo;
  final String secCode;
  final List<Plateno> platenos;

  DriverDc({
    required this.custid,
    required this.mobileno,
    required this.otp,
    required this.serialid,
    required this.plateno,
    required this.fueltype,
    required this.quotavalue,
    required this.remquotavalue,
    required this.stnNo,
    required this.secCode,
    required this.platenos,
  });

  // UPDATED: fromJson factory to match the new API response keys (all uppercase).
  factory DriverDc.fromJson(Map<String, dynamic> json) => DriverDc(
        custid: json["CUSTID"] ?? "",
        mobileno: json["MOBILENO"] ?? "",
        otp: json["OTP"] ?? "",
        serialid: json["SERIALID"] ?? "",
        plateno: json["PLATENO"] ?? "",
        fueltype: json["FUELTYPE"] ?? "",
        quotavalue: json["QUOTAVALUE"] ?? "",
        remquotavalue: json["REMQUOTAVALUE"] ?? "",
        stnNo: json["STN_NO"] ?? "",
        secCode: json["SEC_CODE"] ?? "",
        platenos: json["PLATENOS"] == null
            ? []
            : List<Plateno>.from(
                json["PLATENOS"].map((x) => Plateno.fromJson(x))),
      );

  factory DriverDc.initial() => DriverDc(
      custid: '',
      mobileno: '',
      otp: '',
      serialid: '',
      plateno: '',
      fueltype: '',
      quotavalue: '',
      remquotavalue: '',
      stnNo: '',
      secCode: '',
      platenos: []);
}

class Plateno {
  final String plateNo;
  final String serialid;

  Plateno({
    required this.plateNo,
    required this.serialid,
  });

  // UPDATED: fromJson factory to match the new API response keys (uppercase).
  factory Plateno.fromJson(Map<String, dynamic> json) => Plateno(
        plateNo: json["PLATENO"] ?? "",
        serialid: json["SERIALID"] ?? "",
      );
}
