import 'dart:convert';

// A utility function to safely decode JSON
DriverDc driverDcFromJson(String str) => DriverDc.fromJson(json.decode(str));

class DriverDc {
  final String custid;
  final String mobileno;
  final String otp;
  final String serialid;
  final String plateno;
  final String fueltype;
  final String quotavalue;
  final String remquotavalue;
  final String stnNo;
  final String secCode;
  final List<Plateno> platenos;

  DriverDc({
    required this.custid,
    required this.mobileno,
    required this.otp,
    required this.serialid,
    required this.plateno,
    required this.fueltype,
    required this.quotavalue,
    required this.remquotavalue,
    required this.stnNo,
    required this.secCode,
    required this.platenos,
  });

  factory DriverDc.fromJson(Map<String, dynamic> json) => DriverDc(
        custid: json["custid"] ?? "",
        mobileno: json["mobileno"] ?? "",
        otp: json["otp"] ?? "",
        serialid: json["serialid"] ?? "",
        plateno: json["plateno"] ?? "",
        fueltype: json["fueltype"] ?? "",
        quotavalue: json["quotavalue"] ?? "",
        remquotavalue: json["remquotavalue"] ?? "",
        stnNo: json["stn_no"] ?? "",
        secCode: json["SecCode"] ?? "",
        platenos: json["platenos"] == null
            ? []
            : List<Plateno>.from(
                json["platenos"].map((x) => Plateno.fromJson(x))),
      );

  // A factory for creating an empty/initial state
  factory DriverDc.initial() => DriverDc(
      custid: '',
      mobileno: '',
      otp: '',
      serialid: '',
      plateno: '',
      fueltype: '',
      quotavalue: '',
      remquotavalue: '',
      stnNo: '',
      secCode: '',
      platenos: []);
}

class Plateno {
  final String plateNo;
  final String serialid;

  Plateno({
    required this.plateNo,
    required this.serialid,
  });

  factory Plateno.fromJson(Map<String, dynamic> json) => Plateno(
        plateNo: json["PlateNo"] ?? "",
        serialid: json["serialid"] ?? "",
      );
}
