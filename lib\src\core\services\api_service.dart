import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:waie_digital_coupon_app/src/config/constants/app_constants.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:http/http.dart' as http;
import 'package:retry/retry.dart';

// Custom Exception for API related errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  ApiException(this.message, {this.statusCode});

  @override
  String toString() => "ApiException: $message (Status code: $statusCode)";
}

class ApiService {
  final http.Client client;
  final LoggingService logger;

  ApiService({required this.client, required this.logger});

  Future<dynamic> post(String endpoint,
      {required Map<String, dynamic> body}) async {
    final url = Uri.parse(AppConstants.baseUrl + endpoint);
    logger.info('POST Request to: $url');
    logger.debug('Request Body: $body');

    // Use the retry package to automatically retry on network failures
    const retryOptions = RetryOptions(
      maxAttempts: 3,
      delayFactor: Duration(seconds: 1),
      maxDelay: Duration(seconds: 5),
    );

    try {
      final response = await retryOptions.retry(
        () => client
            .post(
              url,
              headers: {'Content-Type': 'application/json'},
              body: json.encode(body),
            )
            .timeout(const Duration(seconds: 15)),
        retryIf: (e) => e is SocketException || e is TimeoutException,
      );

      logger.debug('Response Status Code: ${response.statusCode}');
      logger.debug('Response Body: ${response.body}');

      return _processResponse(response);
    } on SocketException catch (e, s) {
      logger.error('Network Error during POST request', e, s);
      throw ApiException('Network error. Please check your connection.',
          statusCode: 503);
    } on TimeoutException catch (e, s) {
      logger.error('Request timed out', e, s);
      throw ApiException('The request timed out. Please try again.',
          statusCode: 408);
    } on ApiException {
      rethrow; // Re-throw already processed API exceptions
    } catch (e, s) {
      logger.error('An unexpected error occurred in ApiService', e, s);
      throw ApiException('An unexpected error occurred.');
    }
  }

  dynamic _processResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) {
        return null;
      }
      return json.decode(response.body);
    } else {
      // Handle non-successful status codes
      final errorBody = json.decode(response.body);
      final errorMessage = errorBody['message'] ?? 'An unknown error occurred.';
      throw ApiException(errorMessage, statusCode: response.statusCode);
    }
  }
}
