import 'dart:convert';
import 'package:waie_digital_coupon_app/src/core/models/driver_dc.dart';
import 'package:waie_digital_coupon_app/src/core/providers/auth_provider.dart';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'dart:math';

enum CouponState { initial, loading, detailsLoaded, qrGenerated, error }

class CouponProvider with ChangeNotifier {
  final ApiService _apiService;
  final LoggingService _logger;
  final AuthProvider _authProvider;

  CouponProvider({
    required ApiService apiService,
    required LoggingService logger,
    required AuthProvider authProvider,
  })  : _apiService = apiService,
        _logger = logger,
        _authProvider = authProvider {
    // Listen to changes in AuthProvider
    _authProvider.addListener(_onAuthChanged);
  }

  CouponState _state = CouponState.initial;
  CouponState get state => _state;

  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  DriverDc _driverDetails = DriverDc.initial();
  DriverDc get driverDetails => _driverDetails;

  String _selectedPlateNo = '';
  String get selectedPlateNo => _selectedPlateNo;

  String _qrCodeData = '';
  String get qrCodeData => _qrCodeData;

  String _fillingLiters = '';
  String get fillingLiters => _fillingLiters;

  void _onAuthChanged() {
    if (_authProvider.state == AuthState.verified) {
      _driverDetails = _authProvider.driverDc;
      if (_driverDetails.platenos.isNotEmpty) {
        _selectedPlateNo = _driverDetails.platenos.first.plateNo;
      }
      _setState(CouponState.detailsLoaded);
    }
  }

  void setFillingLiters(String value) {
    _fillingLiters = value;
    notifyListeners();
  }

  Future<void> selectPlateNumber(String plateNumber) async {
    if (_selectedPlateNo == plateNumber) return;

    _selectedPlateNo = plateNumber;
    _setState(CouponState.loading);

    // Find the serial ID for the selected plate number
    final selectedPlate = _driverDetails.platenos.firstWhere(
      (p) => p.plateNo == plateNumber,
      orElse: () => Plateno(plateNo: '', serialid: ''),
    );

    if (selectedPlate.serialid.isEmpty) {
      _setError("Could not find details for the selected plate.");
      return;
    }

    try {
      final body = {
        "mobileno": _driverDetails.mobileno,
        "serialid": selectedPlate.serialid,
        "tokenid": "",
        "IsAr": "false"
      };
      final response = await _apiService
          .post(ApiEndpoints.getDriverServicesDetails, body: body);
      _driverDetails = DriverDc.fromJson(response);
      _setState(CouponState.detailsLoaded);
    } on ApiException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred while fetching plate details.');
    }
  }

  void generateQrCode() {
    if (_fillingLiters.isEmpty ||
        double.tryParse(_fillingLiters) == null ||
        double.parse(_fillingLiters) <= 0) {
      _setError("Please enter a valid amount for filling liters.");
      return;
    }

    _logger.info('Generating QR Code');
    final rng = Random();
    final secNumber = (rng.nextInt(900000) + 100000).toString();
    final serialCode = const Uuid().v4();

    final originalInput = [
      serialCode,
      _driverDetails.custid,
      _driverDetails.mobileno,
      _selectedPlateNo,
      "FUEL",
      _driverDetails.fueltype,
      _fillingLiters,
      "", // Placeholder
      _driverDetails.remquotavalue,
      _driverDetails.serialid,
      _driverDetails.stnNo,
      secNumber,
    ].join("#");

    _qrCodeData = base64.encode(utf8.encode(originalInput));
    _logger.debug('QR Data (encoded): $_qrCodeData');
    _setState(CouponState.qrGenerated);
  }

  void reset() {
    _setState(CouponState.initial);
    _driverDetails = DriverDc.initial();
    _selectedPlateNo = '';
    _qrCodeData = '';
    _fillingLiters = '';
    _errorMessage = '';
  }

  @override
  void dispose() {
    _authProvider.removeListener(_onAuthChanged);
    super.dispose();
  }

  void _setState(CouponState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _state = CouponState.error;
    _logger.error(message);
    notifyListeners();
  }
}
