import 'package:waie_digital_coupon_app/app.dart';
import 'package:waie_digital_coupon_app/src/core/providers/auth_provider.dart';
import 'package:waie_digital_coupon_app/src/core/providers/coupon_provider.dart';
import 'package:waie_digital_coupon_app/src/core/providers/theme_provider.dart';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

void main() async {
  // Ensure that Flutter bindings are initialized before any Flutter code runs.
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  final loggingService = LoggingService();
  final apiService = ApiService(client: http.Client(), logger: loggingService);
  final themeProvider = ThemeProvider();

  // Load the saved theme preference
  await themeProvider.loadTheme();

  runApp(
    // MultiProvider is used to provide multiple objects down the widget tree.
    MultiProvider(
      providers: [
        // Provides the ThemeProvider instance to the widget tree.
        ChangeNotifierProvider(create: (_) => themeProvider),

        // Provides the LoggingService instance.
        Provider<LoggingService>.value(value: loggingService),

        // Provides the ApiService instance.
        Provider<ApiService>.value(value: apiService),

        // Provides AuthProvider, which depends on ApiService and LoggingService.
        ChangeNotifierProvider(
          create: (context) => AuthProvider(
            apiService: context.read<ApiService>(),
            logger: context.read<LoggingService>(),
          ),
        ),

        // Provides CouponProvider, which depends on other providers and services.
        ChangeNotifierProvider(
          create: (context) => CouponProvider(
            apiService: context.read<ApiService>(),
            logger: context.read<LoggingService>(),
            authProvider: context.read<AuthProvider>(),
          ),
        ),
      ],
      child: const DigitalCouponApp(),
    ),
  );
}
