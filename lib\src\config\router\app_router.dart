import 'package:waie_digital_coupon_app/src/ui/screens/confirmation/fuel_confirm_screen.dart';
import 'package:waie_digital_coupon_app/src/ui/screens/create_coupon/create_coupon_screen.dart';
import 'package:waie_digital_coupon_app/src/ui/screens/display_coupon/display_coupon_screen.dart';
import 'package:waie_digital_coupon_app/src/ui/screens/verify_mobile/verify_mobile_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppRouter {
  static const String verifyMobile = '/';
  static const String createCoupon = '/create-coupon';
  static const String displayCoupon = '/display-coupon';
  static const String fuelConfirm = '/confirm-fuel';

  static final GoRouter router = GoRouter(
    initialLocation: verifyMobile,
    routes: <RouteBase>[
      GoRoute(
        path: verifyMobile,
        builder: (BuildContext context, GoRouterState state) {
          return VerifyMobileScreen();
        },
      ),
      GoRoute(
        path: createCoupon,
        builder: (BuildContext context, GoRouterState state) {
          // The 'isFromMenu' parameter determines the back button behavior.
          final bool isFromMenu = (state.extra as bool?) ?? false;
          return CreateCouponScreen(isFromMenu: isFromMenu);
        },
      ),
      GoRoute(
        path: displayCoupon,
        builder: (BuildContext context, GoRouterState state) {
          final bool isFromMenu = (state.extra as bool?) ?? false;
          return DisplayCouponScreen(isFromMenu: isFromMenu);
        },
      ),
      GoRoute(
        path: fuelConfirm,
        builder: (BuildContext context, GoRouterState state) {
          final Map<String, dynamic> args = state.extra as Map<String, dynamic>;
          return FuelConfirmScreen(
            isFromMenu: args['isFromMenu'] ?? false,
            liters: args['liters'] ?? '0',
          );
        },
      ),
    ],
  );
}
