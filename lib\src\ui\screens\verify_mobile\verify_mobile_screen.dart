import 'package:waie_digital_coupon_app/src/config/router/app_router.dart';
import 'package:waie_digital_coupon_app/src/core/providers/auth_provider.dart';
import 'package:waie_digital_coupon_app/src/ui/widgets/common/loading_indicator.dart';
import 'package:waie_digital_coupon_app/src/ui/widgets/common/error_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:provider/provider.dart';
import 'package:flutter_otp_text_field/flutter_otp_text_field.dart';

class VerifyMobileScreen extends StatefulWidget {
  const VerifyMobileScreen({super.key});

  @override
  State<VerifyMobileScreen> createState() => _VerifyMobileScreenState();
}

class _VerifyMobileScreenState extends State<VerifyMobileScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _mobileController = TextEditingController();
  String _fullPhoneNumber = '';

  @override
  void initState() {
    super.initState();
    // Add a listener to handle state changes from the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.addListener(_onAuthStateChanged);
    });
  }

  void _onAuthStateChanged() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.state == AuthState.error) {
      ErrorDisplay.show(context, message: authProvider.errorMessage);
    } else if (authProvider.state == AuthState.verified) {
      context.go(AppRouter.createCoupon);
    }
  }

  @override
  void dispose() {
    Provider.of<AuthProvider>(context, listen: false)
        .removeListener(_onAuthStateChanged);
    _mobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final authProvider = context.watch<AuthProvider>();

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.verification),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                l10n.verifyYourMobileNumber,
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Phone Number Input
              InternationalPhoneNumberInput(
                onInputChanged: (PhoneNumber number) {
                  _fullPhoneNumber = number.phoneNumber ?? '';
                },
                selectorConfig: const SelectorConfig(
                  selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                ),
                ignoreBlank: false,
                autoValidateMode: AutovalidateMode.onUserInteraction,
                textFieldController: _mobileController,
                formatInput: false,
                initialValue: PhoneNumber(isoCode: "SA", dialCode: "SA"),
                keyboardType: const TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                inputDecoration: InputDecoration(
                  labelText: l10n.mobileNumber,
                  hintText: l10n.pleaseEnterHere,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterValidMobile;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              if (authProvider.state != AuthState.otpSent &&
                  authProvider.state != AuthState.verified)
                ElevatedButton(
                  onPressed: authProvider.state == AuthState.loading
                      ? null
                      : () {
                          if (_formKey.currentState!.validate()) {
                            authProvider.verifyMobileNumber(_fullPhoneNumber);
                          }
                        },
                  child: authProvider.state == AuthState.loading
                      ? const LoadingIndicator(size: 20)
                      : Text(l10n.generateOTP),
                ),

              const SizedBox(height: 32),

              // OTP Field
              if (authProvider.state == AuthState.otpSent ||
                  (authProvider.state == AuthState.loading))
                _buildOtpSection(context, l10n, authProvider),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOtpSection(
      BuildContext context, AppLocalizations l10n, AuthProvider authProvider) {
    return Column(
      children: [
        Text(
          l10n.responseCode,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        OtpTextField(
          numberOfFields: 6,
          borderColor: Theme.of(context).colorScheme.primary,
          showFieldAsBox: true,
          onCodeChanged: (String code) {
            // Can be used for real-time validation
          },
          onSubmit: (String verificationCode) {
            authProvider.setEnteredOtp(verificationCode);
            authProvider.verifyOtp();
          },
        ),
        const SizedBox(height: 24),
        if (authProvider.state == AuthState.loading)
          const LoadingIndicator()
        else
          TextButton(
            onPressed: () {
              // Resend OTP logic
              if (_formKey.currentState!.validate()) {
                authProvider.verifyMobileNumber(_fullPhoneNumber);
              }
            },
            child: Text(l10n.resend),
          ),
      ],
    );
  }
}
