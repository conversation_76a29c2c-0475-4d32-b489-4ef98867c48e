import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ErrorDisplay {
  static void show(BuildContext context, {required String message}) {
    final l10n = AppLocalizations.of(context)!;
    final snackBar = SnackBar(
      content: Text(
        _getLocalizedMessage(message, l10n),
        style: TextStyle(color: Theme.of(context).colorScheme.onError),
      ),
      backgroundColor: Theme.of(context).colorScheme.error,
      behavior: SnackBarBehavior.floating,
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  // Maps known error messages to localized strings
  static String _getLocalizedMessage(String message, AppLocalizations l10n) {
    if (message.contains('Invalid OTP')) {
      return l10n.invalidOTP;
    }
    if (message.contains('Mobile is not registered')) {
      return l10n.mobileNotRegistered;
    }
    if (message.contains('Network error')) {
      return l10n.networkError;
    }
    return l10n.somethingWentWrong;
  }
}
