import 'package:waie_digital_coupon_app/src/config/router/app_router.dart';
import 'package:waie_digital_coupon_app/src/core/providers/auth_provider.dart';
import 'package:waie_digital_coupon_app/src/core/providers/coupon_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class FuelConfirmScreen extends StatelessWidget {
  final bool isFromMenu;
  final String liters;
  const FuelConfirmScreen(
      {super.key, required this.isFromMenu, required this.liters});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.digitalCoupon),
        automaticallyImplyLeading: false,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: TextButton(
              onPressed: () {
                // Navigate back to create coupon screen
                context.go(AppRouter.createCoupon, extra: isFromMenu);
              },
              child: Text(l10n.newCoupon),
            ),
          )
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Icon(Icons.check_circle_outline,
                  color: Colors.green, size: 100),
              const SizedBox(height: 24),
              Text(
                l10n.fuelTopUpConfirmed,
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                l10n.yourVehicleFueled(liters),
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              OutlinedButton(
                onPressed: () {
                  // Reset all state and go to the beginning
                  Provider.of<AuthProvider>(context, listen: false).reset();
                  Provider.of<CouponProvider>(context, listen: false).reset();
                  context.go(AppRouter.verifyMobile);
                },
                child: Text(l10n.backToLogin),
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
