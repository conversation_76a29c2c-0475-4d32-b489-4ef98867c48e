import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:waie_digital_coupon_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  // In a real integration test, you would use a mock server (like MockWebServer)
  // to return predictable responses for your API calls. For this example,
  // we will just test the UI flow as much as possible without real network calls.
  // The test will likely fail at the OTP step if not connected to a live or mock backend.

  testWidgets('Full app flow from mobile verification to QR code generation',
      (WidgetTester tester) async {
    // Start the app
    app.main();
    await tester.pumpAndSettle();

    // --- Verify Mobile Screen ---
    expect(find.text('Verification'), findsOneWidget);

    // Find the phone number input field and enter a number
    // Note: Finding widgets in complex trees might require more specific keys.
    final phoneInput = find.byType(InternationalPhoneNumberInput);
    expect(phoneInput, findsOneWidget);
    await tester.enterText(find.byType(TextFormField).first, '501234567');
    await tester.pumpAndSettle();

    // Tap the "Generate OTP" button
    // This will fail if there's no backend to respond.
    // For a real test, you'd mock the http client or use a mock server.
    // await tester.tap(find.text('Generate OTP'));
    // await tester.pumpAndSettle();

    // --- Manually Navigate for Demo ---
    // Since we can't get a real OTP, we'll assume the flow continues.
    // In a real test, you'd get the OTP from the mock server response and enter it.
    // For this demonstration, we can't proceed further without a backend.

    // Example of what would come next:
    // expect(find.text('Response code'), findsOneWidget);
    // await tester.enterText(find.byType(OtpTextField), '123456');
    // await tester.pumpAndSettle();

    // // --- Create Coupon Screen ---
    // expect(find.text('Create a digital coupon'), findsOneWidget);
    // await tester.enterText(find.widgetWithText(TextFormField, 'Filling liters'), '30');
    // await tester.tap(find.text('Generate QR code'));
    // await tester.pumpAndSettle();

    // // --- Display Coupon Screen ---
    // expect(find.text('Digital Coupon'), findsOneWidget);
    // expect(find.byType(QrImageView), findsOneWidget);
  });
}
