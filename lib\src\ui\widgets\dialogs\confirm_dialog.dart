import 'package:digital_coupon_app/src/config/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';

class ConfirmDialog extends StatelessWidget {
  final bool isFromMenu;
  final String liters;
  const ConfirmDialog(
      {super.key, required this.isFromMenu, required this.liters});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(l10n.amount, textAlign: TextAlign.center),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "$liters Liters",
              style: Theme.of(context)
                  .textTheme
                  .headlineMedium
                  ?.copyWith(fontWeight: FontWeight.bold),
            ),
            Text(l10n.petrol91, style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                l10n.attendantScannedQR,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => context.pop(),
          child: Text(l10n.reject),
        ),
        ElevatedButton(
          onPressed: () {
            // Pop the dialog first
            context.pop();
            // Then navigate to the confirmation screen
            context.go(AppRouter.fuelConfirm,
                extra: {'isFromMenu': isFromMenu, 'liters': liters});
          },
          child: Text(l10n.confirm),
        ),
      ],
    );
  }
}
