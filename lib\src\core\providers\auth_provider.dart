import 'dart:convert';

import 'package:waie_digital_coupon_app/src/config/constants/app_constants.dart';
import 'package:waie_digital_coupon_app/src/core/models/driver_dc.dart';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter/material.dart';

enum AuthState { initial, loading, otpSent, verified, error }

class AuthProvider with ChangeNotifier {
  final ApiService _apiService;
  final LoggingService _logger;

  AuthProvider({required ApiService apiService, required LoggingService logger})
      : _apiService = apiService,
        _logger = logger;

  AuthState _state = AuthState.initial;
  AuthState get state => _state;

  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  // DriverDc _driverDc = DriverDc.initial();
  // DriverDc get driverDc => _driverDc;

  String _enteredOtp = '';

  void setEnteredOtp(String otp) {
    _enteredOtp = otp;
  }

  Future<void> verifyMobileNumber(String mobileNumber,
      {bool isAr = false}) async {
    _setState(AuthState.loading);
    _logger.info('RYAN');
    _logger.info(mobileNumber.toString().replaceAll("+", ""));
    _logger.info(isAr);
    try {
      final body = {
        "mobileno": mobileNumber.toString().replaceAll("+", ""),
        "IsAr": isAr ? "true" : "false",
      };
      _logger.info(body);
      final response =
          await _apiService.post(ApiEndpoints.verifyDriverMobileNo, body: body);

      _logger.info('RMV');
      _logger.info(response);
      //Map<String, dynamic> parsedJson = jsonDecode(response.body);

      _logger.info('RMV1');
      _logger.info(jsonDecode(response));
      // Decode the JSON string into a Map
      Map<String, dynamic> rawJson = jsonDecode(response);

      _logger.info(rawJson);
      //Map<String, dynamic> responseMap = jsonDecode(response);
      //_logger.info(responseMap["MOBILENO"]);

      Map<String, dynamic> parsedJson = jsonDecode(response);
      _logger.info(parsedJson);
      DriverDc userResponse = DriverDc.fromJson(response);
      _logger.info(userResponse);
      //_driverDc = DriverDc.fromJson(response);
      //_logger.info(_driverDc.mobileno);
      //_driverDc = DriverDc.fromJson(response);

      _logger.info('RMV2');
      //_logger.info(_driverDc);

      // if (_driverDc.mobileno.length <= 5 || _driverDc.custid.isEmpty) {
      //   _setError('Mobile is not registered with a Digital Coupon account');
      //   return;
      // }

      _setState(AuthState.otpSent);

      _logger.info("OTP");
      _logger.info(AuthState.otpSent);
      _logger.info(
          'OTP Sent successfully. OTP: '); // In a real app, OTP would not be logged.
    } on ApiException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred.');
    }
  }

  Future<bool> verifyOtp() async {
    _logger.info(
        'Verifying OTP. Entered: $_enteredOtp, Expected: ');
    if (_enteredOtp != '') {
      _setError('Invalid OTP');
      return false;
    }

    _setState(AuthState.loading);
    try {
      final body = {
        "mobileno": "_driverDc.mobileno",
        "serialid": "_driverDc.serialid",
        "tokenid": "",
        "IsAr": "false" // Assuming language context is available
      };
      final response = await _apiService
          .post(ApiEndpoints.getDriverServicesDetails, body: body);
      //_driverDc = DriverDc.fromJson(response);
      _setState(AuthState.verified);
      return true;
    } on ApiException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('An unexpected error occurred.');
      return false;
    }
  }

  void reset() {
    _setState(AuthState.initial);
    //_driverDc = DriverDc.initial();
    _errorMessage = '';
    _enteredOtp = '';
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _state = AuthState.error;
    _logger.error(message);
    notifyListeners();
  }
}
