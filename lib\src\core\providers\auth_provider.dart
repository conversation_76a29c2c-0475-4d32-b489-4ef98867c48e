import 'package:waie_digital_coupon_app/src/config/constants/app_constants.dart';
import 'package:waie_digital_coupon_app/src/core/models/driver_dc.dart';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter/material.dart';

enum AuthState { initial, loading, otpSent, verified, error }

class AuthProvider with ChangeNotifier {
  final ApiService _apiService;
  final LoggingService _logger;

  AuthProvider({required ApiService apiService, required LoggingService logger})
      : _apiService = apiService,
        _logger = logger;

  AuthState _state = AuthState.initial;
  AuthState get state => _state;

  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  DriverDc _driverDc = DriverDc.initial();
  DriverDc get driverDc => _driverDc;

  String _enteredOtp = '';

  void setEnteredOtp(String otp) {
    _enteredOtp = otp;
  }

  Future<void> verifyMobileNumber(String mobileNumber,
      {bool isAr = false}) async {
    _setState(AuthState.loading);
    try {
      final body = {
        "mobileno": mobileNumber,
        "IsAr": isAr ? "true" : "false",
      };
      final response =
          await _apiService.post(ApiEndpoints.verifyDriverMobileNo, body: body);
      _driverDc = DriverDc.fromJson(response);

      if (_driverDc.mobileno.length <= 5 || _driverDc.custid.isEmpty) {
        _setError('Mobile is not registered with a Digital Coupon account');
        return;
      }

      _setState(AuthState.otpSent);
      _logger.info(
          'OTP Sent successfully. OTP: ${_driverDc.otp}'); // In a real app, OTP would not be logged.
    } on ApiException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred.');
    }
  }

  Future<bool> verifyOtp() async {
    _logger.info(
        'Verifying OTP. Entered: $_enteredOtp, Expected: ${_driverDc.otp}');
    if (_enteredOtp != _driverDc.otp) {
      _setError('Invalid OTP');
      return false;
    }

    _setState(AuthState.loading);
    try {
      final body = {
        "mobileno": _driverDc.mobileno,
        "serialid": _driverDc.serialid,
        "tokenid": "",
        "IsAr": "false" // Assuming language context is available
      };
      final response = await _apiService
          .post(ApiEndpoints.getDriverServicesDetails, body: body);
      _driverDc = DriverDc.fromJson(response);
      _setState(AuthState.verified);
      return true;
    } on ApiException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('An unexpected error occurred.');
      return false;
    }
  }

  void reset() {
    _setState(AuthState.initial);
    _driverDc = DriverDc.initial();
    _errorMessage = '';
    _enteredOtp = '';
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _state = AuthState.error;
    _logger.error(message);
    notifyListeners();
  }
}
