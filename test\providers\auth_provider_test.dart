import 'package:waie_digital_coupon_app/src/core/providers/auth_provider.dart';
import 'package:waie_digital_coupon_app/src/core/services/api_service.dart';
import 'package:waie_digital_coupon_app/src/core/services/logging_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'auth_provider_test.mocks.dart';

@GenerateMocks([ApiService, LoggingService])
void main() {
  late MockApiService mockApiService;
  late MockLoggingService mockLogger;
  late AuthProvider authProvider;

  setUp(() {
    mockApiService = MockApiService();
    mockLogger = MockLoggingService();
    authProvider = AuthProvider(apiService: mockApiService, logger: mockLogger);
  });

  group('AuthProvider', () {
    final validMobileResponse = {
      "custid": "12345",
      "mobileno": "966500000000",
      "otp": "123456",
      "serialid": "serial1",
      "platenos": [
        {"PlateNo": "ABC 123", "serialid": "serial1"}
      ]
    };

    test('initial state is correct', () {
      expect(authProvider.state, AuthState.initial);
    });

    test('verifyMobileNumber success sets state to otpSent', () async {
      when(mockApiService.post(any, body: anyNamed('body')))
          .thenAnswer((_) async => validMobileResponse);

      final future = authProvider.verifyMobileNumber('966500000000');

      expect(authProvider.state, AuthState.loading);

      await future;

      expect(authProvider.state, AuthState.otpSent);
      expect(authProvider.driverDc.otp, '123456');
    });

    test('verifyMobileNumber failure sets state to error', () async {
      when(mockApiService.post(any, body: anyNamed('body')))
          .thenThrow(ApiException('Mobile not found'));

      await authProvider.verifyMobileNumber('966500000000');

      expect(authProvider.state, AuthState.error);
      expect(authProvider.errorMessage, 'Mobile not found');
    });

    test('verifyOtp success sets state to verified', () async {
      // First, simulate a successful mobile verification to set up the state
      when(mockApiService.post(any, body: anyNamed('body')))
          .thenAnswer((_) async => validMobileResponse);
      await authProvider.verifyMobileNumber('966500000000');

      // Now, test OTP verification
      authProvider.setEnteredOtp('123456');

      // Mock the second API call for getting service details
      when(mockApiService.post(any, body: anyNamed('body')))
          .thenAnswer((_) async => validMobileResponse);

      final result = await authProvider.verifyOtp();

      expect(result, isTrue);
      expect(authProvider.state, AuthState.verified);
    });
    test('verifyOtp failure returns false and sets error', () async {
      // First, simulate a successful mobile verification to set up the state
      when(mockApiService.post(any, body: anyNamed('body')))
          .thenAnswer((_) async => validMobileResponse);
      await authProvider.verifyMobileNumber('966500000000');

      // Now, test OTP verification with wrong OTP
      authProvider.setEnteredOtp('654321');

      final result = await authProvider.verifyOtp();

      expect(result, isFalse);
      expect(authProvider.state, AuthState.error);
      expect(authProvider.errorMessage, 'Invalid OTP');
    });
  });
}
