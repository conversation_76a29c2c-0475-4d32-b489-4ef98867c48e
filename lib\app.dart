import 'package:waie_digital_coupon_app/src/config/router/app_router.dart';
import 'package:waie_digital_coupon_app/src/core/providers/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class DigitalCouponApp extends StatelessWidget {
  const DigitalCouponApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Watch for changes in the ThemeProvider to rebuild the UI with the new theme.
    final themeProvider = context.watch<ThemeProvider>();

    return MaterialApp.router(
      title: 'Digital Coupon App',

      // --- Localization ---
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      // You can set a default locale here, e.g., locale: Locale('ar'),

      // --- Theme ---
      theme: themeProvider.lightTheme,
      darkTheme: themeProvider.darkTheme,
      themeMode: themeProvider.themeMode,

      // --- Routing ---
      routerConfig: AppRouter.router,

      debugShowCheckedModeBanner: false,
    );
  }
}
